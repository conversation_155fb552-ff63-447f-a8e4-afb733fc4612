#!/usr/bin/env python3
"""
Test script for Vision Context MCP Server
"""

import asyncio
import json
import sys
from typing import Dict, Any

import structlog
from vision_context_mcp.server import VisionContextMCPServer
from vision_context_mcp.config.settings import get_settings
from vision_context_mcp.utils.helpers import setup_logging

logger = structlog.get_logger(__name__)


async def test_screen_capture():
    """Test screen capture functionality"""
    logger.info("Testing screen capture...")
    
    from vision_context_mcp.core.screen_capture import ScreenCapture
    
    screen_capture = ScreenCapture()
    
    # Test fullscreen capture
    image = await screen_capture.capture_fullscreen()
    if image:
        logger.info("Fullscreen capture successful", size=image.size)
    else:
        logger.error("Fullscreen capture failed")
    
    # Test active window capture
    image = await screen_capture.capture_active_window()
    if image:
        logger.info("Active window capture successful", size=image.size)
    else:
        logger.warning("Active window capture failed, this is normal on some systems")


async def test_vision_llm():
    """Test Vision LLM functionality"""
    logger.info("Testing Vision LLM...")
    
    from vision_context_mcp.core.vision_llm import VisionLLM
    
    vision_llm = VisionLLM()
    available_models = vision_llm.get_available_models()
    
    if available_models:
        logger.info("Available Vision LLM models", models=available_models)
    else:
        logger.warning("No Vision LLM models available. Please configure API keys.")


async def test_context_engine():
    """Test Context Engine functionality"""
    logger.info("Testing Context Engine...")
    
    from vision_context_mcp.core.context_engine import ContextEngine
    
    context_engine = ContextEngine()
    
    # Test starting and stopping
    await context_engine.start()
    logger.info("Context engine started")
    
    # Wait a bit to collect some events
    await asyncio.sleep(3)
    
    # Get context summary
    summary = context_engine.get_context_summary()
    logger.info("Context summary", summary=summary)
    
    # Get recent events
    events = context_engine.get_recent_events(limit=5)
    logger.info("Recent events", count=len(events))
    
    await context_engine.stop()
    logger.info("Context engine stopped")


async def test_mcp_tools():
    """Test MCP tools"""
    logger.info("Testing MCP tools...")
    
    from vision_context_mcp.tools import ScreenAnalyzerTool, VideoRecorderTool, VisionQueryTool
    
    # Test screen analyzer
    screen_analyzer = ScreenAnalyzerTool()
    try:
        result = await screen_analyzer.execute({"mode": "fullscreen"})
        logger.info("Screen analyzer test successful", result_count=len(result))
    except Exception as e:
        logger.error("Screen analyzer test failed", error=str(e))
    
    # Test video recorder (short duration)
    video_recorder = VideoRecorderTool()
    try:
        result = await video_recorder.execute({"duration": 2})
        logger.info("Video recorder test successful", result_count=len(result))
    except Exception as e:
        logger.error("Video recorder test failed", error=str(e))
    
    # Test vision query
    vision_query = VisionQueryTool()
    try:
        result = await vision_query.execute({"question": "Test sorusu: Bu görüntüde ne var?"})
        logger.info("Vision query test successful", result_count=len(result))
    except Exception as e:
        logger.error("Vision query test failed", error=str(e))


async def test_server_initialization():
    """Test server initialization"""
    logger.info("Testing server initialization...")
    
    try:
        server = VisionContextMCPServer()
        logger.info("Server initialization successful")
        
        # Test tool listing
        tools = server.tools
        logger.info("Available tools", tools=list(tools.keys()))
        
    except Exception as e:
        logger.error("Server initialization failed", error=str(e))


async def run_all_tests():
    """Run all tests"""
    logger.info("Starting Vision Context MCP Server tests")
    
    tests = [
        ("Screen Capture", test_screen_capture),
        ("Vision LLM", test_vision_llm),
        ("Context Engine", test_context_engine),
        ("MCP Tools", test_mcp_tools),
        ("Server Initialization", test_server_initialization),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"Running test: {test_name}")
        try:
            await test_func()
            results[test_name] = "PASSED"
            logger.info(f"Test {test_name} PASSED")
        except Exception as e:
            results[test_name] = f"FAILED: {str(e)}"
            logger.error(f"Test {test_name} FAILED", error=str(e))
        
        logger.info("---")
    
    # Print summary
    logger.info("Test Summary:")
    for test_name, result in results.items():
        logger.info(f"  {test_name}: {result}")
    
    passed_count = sum(1 for r in results.values() if r == "PASSED")
    total_count = len(results)
    
    logger.info(f"Tests completed: {passed_count}/{total_count} passed")
    
    return passed_count == total_count


def main():
    """Main test function"""
    # Setup logging
    setup_logging(level="INFO", use_rich=True)
    
    # Get settings
    settings = get_settings()
    logger.info("Test configuration", 
               debug=settings.debug,
               available_apis={
                   "anthropic": bool(settings.anthropic_api_key),
                   "openai": bool(settings.openai_api_key),
                   "google": bool(settings.google_api_key)
               })
    
    # Run tests
    try:
        success = asyncio.run(run_all_tests())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error("Test execution failed", error=str(e))
        sys.exit(1)


if __name__ == "__main__":
    main()
