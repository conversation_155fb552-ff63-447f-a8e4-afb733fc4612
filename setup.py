#!/usr/bin/env python3
"""
Setup script for Vision Context MCP Server
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def run_command(command, check=True):
    """Run a command and return the result"""
    print(f"Running: {command}")
    result = subprocess.run(command, shell=True, capture_output=True, text=True)
    
    if check and result.returncode != 0:
        print(f"Error running command: {command}")
        print(f"stdout: {result.stdout}")
        print(f"stderr: {result.stderr}")
        sys.exit(1)
    
    return result

def check_python_version():
    """Check Python version"""
    if sys.version_info < (3, 9):
        print("Error: Python 3.9 or higher is required")
        sys.exit(1)
    
    print(f"✓ Python version: {sys.version}")

def install_dependencies():
    """Install Python dependencies"""
    print("Installing Python dependencies...")
    
    # Upgrade pip first
    run_command(f"{sys.executable} -m pip install --upgrade pip")
    
    # Install requirements
    run_command(f"{sys.executable} -m pip install -r requirements.txt")
    
    print("✓ Dependencies installed")

def setup_environment():
    """Setup environment file"""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists() and env_example.exists():
        print("Creating .env file from .env.example...")
        shutil.copy(env_example, env_file)
        print("✓ .env file created")
        print("⚠️  Please edit .env file and add your API keys")
    else:
        print("✓ .env file already exists")

def test_installation():
    """Test the installation"""
    print("Testing installation...")
    
    try:
        # Test import
        import vision_context_mcp
        print("✓ Package import successful")
        
        # Run basic tests
        result = run_command(f"{sys.executable} test_server.py", check=False)
        if result.returncode == 0:
            print("✓ Basic tests passed")
        else:
            print("⚠️  Some tests failed, but installation is complete")
            print("This is normal if API keys are not configured")
    
    except ImportError as e:
        print(f"✗ Package import failed: {e}")
        sys.exit(1)

def setup_claude_desktop():
    """Setup Claude Desktop configuration"""
    print("Setting up Claude Desktop configuration...")
    
    # Common Claude Desktop config paths
    config_paths = []
    
    if sys.platform == "win32":
        # Windows
        appdata = os.environ.get("APPDATA")
        if appdata:
            config_paths.append(Path(appdata) / "Claude" / "claude_desktop_config.json")
    elif sys.platform == "darwin":
        # macOS
        home = Path.home()
        config_paths.append(home / "Library" / "Application Support" / "Claude" / "claude_desktop_config.json")
    else:
        # Linux
        home = Path.home()
        config_paths.append(home / ".config" / "Claude" / "claude_desktop_config.json")
    
    # Find existing config
    existing_config = None
    for path in config_paths:
        if path.exists():
            existing_config = path
            break
    
    current_dir = Path.cwd()
    config_content = f'''{{
  "mcpServers": {{
    "vision-context-mcp": {{
      "command": "python",
      "args": [
        "-m", 
        "vision_context_mcp.server"
      ],
      "cwd": "{current_dir}",
      "env": {{
        "VISION_MCP_DEBUG": "false",
        "VISION_MCP_LOG_LEVEL": "INFO"
      }}
    }}
  }}
}}'''
    
    if existing_config:
        print(f"Found existing Claude Desktop config at: {existing_config}")
        print("Please manually add the following configuration to your claude_desktop_config.json:")
        print(config_content)
    else:
        print("Claude Desktop config file not found.")
        print("Please create claude_desktop_config.json in the appropriate location:")
        for path in config_paths:
            print(f"  {path}")
        print("\nWith the following content:")
        print(config_content)
    
    # Also save to current directory
    with open("claude_desktop_config.json", "w") as f:
        f.write(config_content)
    
    print("✓ Configuration saved to claude_desktop_config.json")

def main():
    """Main setup function"""
    print("🧠 Vision Context MCP Server Setup")
    print("=" * 40)
    
    # Check Python version
    check_python_version()
    
    # Install dependencies
    install_dependencies()
    
    # Setup environment
    setup_environment()
    
    # Test installation
    test_installation()
    
    # Setup Claude Desktop
    setup_claude_desktop()
    
    print("\n" + "=" * 40)
    print("✅ Setup completed!")
    print("\nNext steps:")
    print("1. Edit .env file and add your API keys")
    print("2. Add the MCP server configuration to Claude Desktop")
    print("3. Restart Claude Desktop")
    print("4. Test the tools in Claude Desktop")
    print("\nFor testing, run: python test_server.py")

if __name__ == "__main__":
    main()
