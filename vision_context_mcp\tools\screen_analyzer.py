"""
Screen Analyzer Tool for Vision Context MCP Server
"""

from typing import Any, Dict, List, Union

from mcp.types import TextContent, ImageContent

from .base import BaseTool
from ..core.screen_capture import ScreenCapture
from ..core.vision_llm import VisionLLM
from ..utils.helpers import encode_image_to_base64


class ScreenAnalyzerTool(BaseTool):
    """Tool for analyzing screen content"""
    
    def __init__(self):
        super().__init__(
            name="analyze_screen_context",
            description="Aktif pencere veya tam ekranı analiz eder"
        )
        self.screen_capture = ScreenCapture()
        self.vision_llm = VisionLLM()
    
    async def execute(
        self, 
        arguments: Dict[str, Any]
    ) -> List[Union[TextContent, ImageContent]]:
        """
        Execute screen analysis
        
        Args:
            arguments: Tool arguments containing mode and optional analysis_prompt
            
        Returns:
            Analysis results as content list
        """
        self._log_execution_start(arguments)
        
        try:
            # Validate arguments
            self._validate_arguments(arguments, ["mode"])
            
            mode = arguments["mode"]
            analysis_prompt = arguments.get(
                "analysis_prompt", 
                "<PERSON>u görüntüde ne görüyorsun? Detaylı analiz yap."
            )
            
            # Capture screen
            if mode == "fullscreen":
                image = await self.screen_capture.capture_fullscreen()
            elif mode == "active_window":
                image = await self.screen_capture.capture_active_window()
            else:
                raise ValueError(f"Geçersiz mode: {mode}")
            
            if image is None:
                return [self._create_text_content(
                    "Ekran görüntüsü alınamadı. Lütfen tekrar deneyin."
                )]
            
            # Encode image to base64
            image_base64 = encode_image_to_base64(image)
            
            # Analyze with Vision LLM
            analysis_result = await self.vision_llm.analyze_image(
                image_base64, 
                prompt=analysis_prompt
            )
            
            self._log_execution_end(success=True)
            
            # Return both the analysis and the captured image
            return [
                self._create_text_content(
                    f"📸 Ekran Analizi ({mode}):\n\n{analysis_result}"
                ),
                self._create_image_content(
                    data=image_base64,
                    mime_type="image/png"
                )
            ]
            
        except Exception as e:
            self._log_execution_end(success=False, error=str(e))
            return [self._create_text_content(
                f"Ekran analizi sırasında hata oluştu: {str(e)}"
            )]
