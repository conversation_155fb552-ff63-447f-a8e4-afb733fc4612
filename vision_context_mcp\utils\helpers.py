"""
Helper functions for Vision Context MCP Server
"""

import base64
import io
import logging
import sys
from datetime import datetime
from typing import Optional, Union

import structlog
from PIL import Image
from rich.console import Console
from rich.logging import RichHandler


def encode_image_to_base64(image: Union[Image.Image, bytes, str]) -> str:
    """
    Encode image to base64 string
    
    Args:
        image: PIL Image, bytes, or file path
        
    Returns:
        Base64 encoded string
    """
    if isinstance(image, str):
        # File path
        with open(image, "rb") as f:
            image_bytes = f.read()
    elif isinstance(image, Image.Image):
        # PIL Image
        buffer = io.BytesIO()
        image.save(buffer, format="PNG")
        image_bytes = buffer.getvalue()
    elif isinstance(image, bytes):
        # Raw bytes
        image_bytes = image
    else:
        raise ValueError(f"Unsupported image type: {type(image)}")
    
    return base64.b64encode(image_bytes).decode("utf-8")


def get_timestamp() -> str:
    """Get current timestamp in ISO format"""
    return datetime.now().isoformat()


def validate_image_format(image_path: str) -> bool:
    """
    Validate if file is a supported image format
    
    Args:
        image_path: Path to image file
        
    Returns:
        True if valid image format
    """
    try:
        with Image.open(image_path) as img:
            img.verify()
        return True
    except Exception:
        return False


def setup_logging(
    level: str = "INFO",
    log_file: Optional[str] = None,
    use_rich: bool = True
) -> None:
    """
    Setup structured logging with rich formatting
    
    Args:
        level: Logging level
        log_file: Optional log file path
        use_rich: Use rich formatting for console output
    """
    # Configure structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    # Setup standard logging
    handlers = []
    
    if use_rich:
        console = Console()
        rich_handler = RichHandler(
            console=console,
            show_time=True,
            show_path=True,
            markup=True,
            rich_tracebacks=True
        )
        handlers.append(rich_handler)
    else:
        handlers.append(logging.StreamHandler(sys.stdout))
    
    if log_file:
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(
            logging.Formatter(
                "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            )
        )
        handlers.append(file_handler)
    
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        handlers=handlers,
        format="%(message)s"
    )


def resize_image(
    image: Image.Image,
    max_size: tuple[int, int] = (1920, 1080),
    maintain_aspect: bool = True
) -> Image.Image:
    """
    Resize image to maximum dimensions
    
    Args:
        image: PIL Image to resize
        max_size: Maximum (width, height)
        maintain_aspect: Maintain aspect ratio
        
    Returns:
        Resized PIL Image
    """
    if maintain_aspect:
        image.thumbnail(max_size, Image.Resampling.LANCZOS)
    else:
        image = image.resize(max_size, Image.Resampling.LANCZOS)
    
    return image


def get_system_info() -> dict:
    """Get system information"""
    import platform
    import psutil
    
    return {
        "platform": platform.system(),
        "platform_version": platform.version(),
        "architecture": platform.architecture()[0],
        "processor": platform.processor(),
        "memory_total": psutil.virtual_memory().total,
        "memory_available": psutil.virtual_memory().available,
        "cpu_count": psutil.cpu_count(),
        "python_version": sys.version,
    }
