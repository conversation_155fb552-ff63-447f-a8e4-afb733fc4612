# MCP Protocol ve JSON-RPC
mcp>=1.0.0
jsonrpcserver>=5.0.0
pydantic>=2.0.0

# Ekran yakalama ve görüntü işleme
mss>=9.0.0
pygetwindow>=0.0.9
pyautogui>=0.9.54
Pillow>=10.0.0
opencv-python>=4.8.0

# Video kayıt
av>=10.0.0

# Vision LLM API'leri
anthropic>=0.25.0
openai>=1.0.0
google-generativeai>=0.3.0

# Async ve utility
aiohttp>=3.9.0
asyncio-mqtt>=0.13.0
python-dotenv>=1.0.0

# Logging ve monitoring
structlog>=23.0.0
rich>=13.0.0

# Platform specific (Windows)
pywin32>=306; sys_platform == "win32"
pygetwindow>=0.0.9; sys_platform == "win32"

# Platform specific (macOS)
pyobjc-framework-Quartz>=9.0; sys_platform == "darwin"
pyobjc-framework-ApplicationServices>=9.0; sys_platform == "darwin"

# Platform specific (Linux)
python-xlib>=0.33; sys_platform == "linux"
pycairo>=1.20.0; sys_platform == "linux"

# Additional dependencies
psutil>=5.9.0
