"""
Vision Query Tool for Vision Context MCP Server
"""

from typing import Any, Dict, List, Union

from mcp.types import TextContent, ImageContent

from .base import BaseTool
from ..core.screen_capture import ScreenCapture
from ..core.vision_llm import VisionLLM
from ..utils.helpers import encode_image_to_base64


class VisionQueryTool(BaseTool):
    """Tool for querying vision LLM about current view"""
    
    def __init__(self):
        super().__init__(
            name="query_vision_about_current_view",
            description="Görsel analiz sonucu hakkında Vision LLM'e soru sorar"
        )
        self.screen_capture = ScreenCapture()
        self.vision_llm = VisionLLM()
    
    async def execute(
        self, 
        arguments: Dict[str, Any]
    ) -> List[Union[TextContent, ImageContent]]:
        """
        Execute vision query
        
        Args:
            arguments: Tool arguments containing question and optional context
            
        Returns:
            Query results as content list
        """
        self._log_execution_start(arguments)
        
        try:
            # Validate arguments
            self._validate_arguments(arguments, ["question"])
            
            question = arguments["question"]
            context = arguments.get("context", "")
            
            # Capture current screen (active window by default for queries)
            image = await self.screen_capture.capture_active_window()
            
            if image is None:
                # Fallback to fullscreen if active window capture fails
                image = await self.screen_capture.capture_fullscreen()
            
            if image is None:
                return [self._create_text_content(
                    "Ekran görüntüsü alınamadı. Lütfen tekrar deneyin."
                )]
            
            # Encode image to base64
            image_base64 = encode_image_to_base64(image)
            
            # Prepare prompt with context
            full_prompt = question
            if context:
                full_prompt = f"Bağlam: {context}\n\nSoru: {question}"
            
            # Query Vision LLM
            response = await self.vision_llm.analyze_image(
                image_base64,
                prompt=full_prompt
            )
            
            # Check if this is a follow-up question
            conversation_context = await self.vision_llm.get_conversation_context()
            
            self._log_execution_end(success=True)
            
            # Prepare response
            result_text = f"""🤔 Vision LLM Sorgusu:

❓ Soru: {question}
{f'📝 Bağlam: {context}' if context else ''}

🤖 Yanıt:
{response}

💭 Sohbet Bağlamı: {len(conversation_context)} önceki mesaj"""
            
            return [
                self._create_text_content(result_text),
                self._create_image_content(
                    data=image_base64,
                    mime_type="image/png"
                )
            ]
            
        except Exception as e:
            self._log_execution_end(success=False, error=str(e))
            return [self._create_text_content(
                f"Vision sorgusu sırasında hata oluştu: {str(e)}"
            )]
