# Vision Context MCP Server Configuration

# Server Settings
VISION_MCP_SERVER_HOST=localhost
VISION_MCP_SERVER_PORT=8000
VISION_MCP_DEBUG=false

# Vision LLM API Keys
VISION_MCP_ANTHROPIC_API_KEY=your_anthropic_api_key_here
VISION_MCP_OPENAI_API_KEY=your_openai_api_key_here
VISION_MCP_GOOGLE_API_KEY=your_google_api_key_here

# Screen Capture Settings
VISION_MCP_CAPTURE_QUALITY=95
VISION_MCP_CAPTURE_FORMAT=PNG

# Video Recording Settings
VISION_MCP_VIDEO_FPS=30
VISION_MCP_VIDEO_QUALITY=23
VISION_MCP_MAX_RECORDING_DURATION=300

# Context Engine Settings
VISION_MCP_CONTEXT_CHECK_INTERVAL=1.0
VISION_MCP_MAX_CONTEXT_HISTORY=100

# Logging
VISION_MCP_LOG_LEVEL=INFO
VISION_MCP_LOG_FILE=vision_mcp.log

# Security
VISION_MCP_REQUIRE_AUTH=false
