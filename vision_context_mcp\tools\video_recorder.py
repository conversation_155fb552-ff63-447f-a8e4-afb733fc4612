"""
Video Recorder Tool for Vision Context MCP Server
"""

import asyncio
import os
import tempfile
from typing import Any, Dict, List, Union

from mcp.types import TextContent, ImageContent

from .base import BaseTool
from ..core.screen_capture import ScreenCapture
from ..core.vision_llm import VisionLLM
from ..utils.helpers import encode_image_to_base64, get_timestamp


class VideoRecorderTool(BaseTool):
    """Tool for recording screen video"""
    
    def __init__(self):
        super().__init__(
            name="record_screen",
            description="Ekranı belirtilen süre boyunca kaydeder"
        )
        self.screen_capture = ScreenCapture()
        self.vision_llm = VisionLLM()
    
    async def execute(
        self, 
        arguments: Dict[str, Any]
    ) -> List[Union[TextContent, ImageContent]]:
        """
        Execute screen recording
        
        Args:
            arguments: Tool arguments containing duration and optional mode
            
        Returns:
            Recording results and analysis
        """
        self._log_execution_start(arguments)
        
        try:
            # Validate arguments
            self._validate_arguments(arguments, ["duration"])
            
            duration = arguments["duration"]
            mode = arguments.get("mode", "fullscreen")
            
            # Validate duration
            if duration < 1 or duration > 300:
                raise ValueError("Kayıt süresi 1-300 saniye arasında olmalıdır")
            
            # Create temporary file for video
            temp_dir = tempfile.gettempdir()
            timestamp = get_timestamp().replace(":", "-").replace(".", "-")
            video_filename = f"screen_recording_{timestamp}.mp4"
            video_path = os.path.join(temp_dir, video_filename)
            
            # Start recording
            self.logger.info("Starting screen recording", duration=duration, mode=mode)
            
            recording_task = asyncio.create_task(
                self.screen_capture.record_screen(
                    duration=duration,
                    output_path=video_path,
                    mode=mode
                )
            )
            
            # Wait for recording to complete
            await recording_task
            
            # Verify video file exists
            if not os.path.exists(video_path):
                raise FileNotFoundError("Video dosyası oluşturulamadı")
            
            file_size = os.path.getsize(video_path)
            
            # Capture a frame from the video for analysis
            frame_image = await self.screen_capture.extract_frame_from_video(
                video_path, 
                timestamp_sec=duration // 2  # Middle frame
            )
            
            analysis_result = ""
            if frame_image:
                frame_base64 = encode_image_to_base64(frame_image)
                analysis_result = await self.vision_llm.analyze_image(
                    frame_base64,
                    prompt=f"Bu {duration} saniyelik ekran kaydından bir kare. Ne olduğunu analiz et."
                )
            
            self._log_execution_end(success=True)
            
            # Prepare result
            result_text = f"""🎥 Ekran Kaydı Tamamlandı:

📁 Dosya: {video_filename}
📍 Konum: {video_path}
⏱️ Süre: {duration} saniye
📊 Boyut: {file_size / 1024 / 1024:.2f} MB
🎯 Mod: {mode}

📝 Analiz:
{analysis_result if analysis_result else 'Analiz yapılamadı'}

💡 Not: Video dosyası geçici dizinde saklanmıştır."""
            
            content = [self._create_text_content(result_text)]
            
            # Add frame image if available
            if frame_image:
                frame_base64 = encode_image_to_base64(frame_image)
                content.append(self._create_image_content(
                    data=frame_base64,
                    mime_type="image/png"
                ))
            
            return content
            
        except Exception as e:
            self._log_execution_end(success=False, error=str(e))
            return [self._create_text_content(
                f"Video kaydı sırasında hata oluştu: {str(e)}"
            )]
