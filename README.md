# 🧠 Vision Context MCP Server

Bu MCP server, yapay zeka modellerine (<PERSON>, GPT, vb.) ger<PERSON><PERSON> zamanlı görsel algı yeteneği kazandırmak için tasarlanmıştır.

## 🎯 Özellikler

- **Gerçek Zamanlı Ekran Analizi**: Aktif pencere veya tam ekran görüntüsü analizi
- **Video Kayıt**: Belirtilen süre boyunca ekran kaydı
- **Vision LLM Entegrasyonu**: <PERSON> VLM, GPT-4V ile görsel analiz
- **Context Engine**: Arka planda çalışan olay izleme sistemi
- **MCP Protokol Desteği**: JSON-RPC 2.0 üzerinden Claude Desktop entegrasyonu

## 🚀 Kurulum

```bash
# Bağımlılıkları yükle
pip install -r requirements.txt

# MCP server'ı başlat
python -m vision_context_mcp
```

## 📦 Proje <PERSON>

```
vision_context_mcp/
├── __init__.py
├── server.py              # Ana MCP server
├── tools/                 # MCP tool implementasyonları
│   ├── __init__.py
│   ├── screen_analyzer.py
│   ├── video_recorder.py
│   └── vision_query.py
├── core/                  # Temel modüller
│   ├── __init__.py
│   ├── screen_capture.py  # Ekran yakalama
│   ├── vision_llm.py      # Vision LLM entegrasyonu
│   └── context_engine.py  # Arka plan analiz motoru
├── config/
│   ├── __init__.py
│   └── settings.py        # Konfigürasyon
└── utils/
    ├── __init__.py
    └── helpers.py         # Yardımcı fonksiyonlar
```

## 🔧 Kullanım

MCP server Claude Desktop ile entegre edildikten sonra aşağıdaki tool'ları kullanabilirsiniz:

- `analyze_screen_context`: Ekran analizi
- `record_screen`: Video kayıt
- `query_vision_about_current_view`: Vision LLM'e soru sorma

## 📄 Lisans

MIT License
