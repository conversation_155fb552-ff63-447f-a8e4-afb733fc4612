# 🧠 Vision Context MCP Server

A Model Context Protocol (MCP) server that provides real-time visual perception capabilities to AI models like <PERSON>, GP<PERSON>, and others. This server enables AI assistants to see and analyze screen content, record videos, and maintain visual context awareness.

## 🎯 Features

- **Real-time Screen Analysis**: Capture and analyze fullscreen or active window content
- **Video Recording**: Record screen activity for specified durations (1-300 seconds)
- **Multi-LLM Vision Support**: Integrated support for Claude VLM, GPT-4V, and Gemini Pro Vision
- **Background Context Engine**: Monitors window changes, application switches, and system events
- **MCP Protocol Compliance**: Full JSON-RPC 2.0 implementation for Claude Desktop integration
- **Cross-platform Support**: Works on Windows, macOS, and Linux
- **Conversation Context**: Maintains chat history for follow-up visual queries

## 🚀 Quick Start

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd vision

# Run the setup script
python setup.py
```

### Configuration

1. Copy `.env.example` to `.env` and configure your API keys:
```bash
cp .env.example .env
```

2. Edit `.env` file and add at least one Vision LLM API key:
```env
VISION_MCP_ANTHROPIC_API_KEY=your_anthropic_api_key_here
VISION_MCP_OPENAI_API_KEY=your_openai_api_key_here
VISION_MCP_GOOGLE_API_KEY=your_google_api_key_here
```

### Testing

```bash
# Run basic functionality tests
python test_server.py

# Start the MCP server manually
python -m vision_context_mcp
```

## 📦 Project Structure

```
vision_context_mcp/
├── __init__.py
├── server.py              # Main MCP server implementation
├── tools/                 # MCP tool implementations
│   ├── __init__.py
│   ├── base.py           # Base tool class
│   ├── screen_analyzer.py # Screen analysis tool
│   ├── video_recorder.py  # Video recording tool
│   └── vision_query.py    # Vision query tool
├── core/                  # Core modules
│   ├── __init__.py
│   ├── screen_capture.py  # Screen capture functionality
│   ├── vision_llm.py      # Vision LLM integration
│   └── context_engine.py  # Background context monitoring
├── config/
│   ├── __init__.py
│   └── settings.py        # Configuration management
└── utils/
    ├── __init__.py
    └── helpers.py         # Utility functions
```

## 🔧 Claude Desktop Integration

### Add to Claude Desktop Configuration

Add the following to your Claude Desktop `claude_desktop_config.json`:

```json
{
  "mcpServers": {
    "vision-context-mcp": {
      "command": "python",
      "args": ["-m", "vision_context_mcp.server"],
      "cwd": "/path/to/your/vision/directory",
      "env": {
        "VISION_MCP_DEBUG": "false",
        "VISION_MCP_LOG_LEVEL": "INFO"
      }
    }
  }
}
```

### Configuration File Locations

- **Windows**: `%APPDATA%\Claude\claude_desktop_config.json`
- **macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`
- **Linux**: `~/.config/Claude/claude_desktop_config.json`

## 🛠️ Available MCP Tools

### 1. `analyze_screen_context`
Captures and analyzes screen content using Vision LLM.

**Parameters:**
- `mode` (required): `"fullscreen"` or `"active_window"`
- `analysis_prompt` (optional): Custom analysis prompt

**Example:**
```json
{
  "mode": "active_window",
  "analysis_prompt": "What application is this and what is the user doing?"
}
```

### 2. `record_screen`
Records screen activity for a specified duration.

**Parameters:**
- `duration` (required): Recording duration in seconds (1-300)
- `mode` (optional): `"fullscreen"` or `"active_window"` (default: "fullscreen")

**Example:**
```json
{
  "duration": 10,
  "mode": "fullscreen"
}
```

### 3. `query_vision_about_current_view`
Asks specific questions about the current screen content.

**Parameters:**
- `question` (required): Question to ask about the current view
- `context` (optional): Additional context for the query

**Example:**
```json
{
  "question": "What errors do you see on this screen?",
  "context": "I'm debugging a web application"
}
```

## 🔍 Technical Architecture

### Core Components

1. **MCP Server**: JSON-RPC 2.0 compliant server handling tool calls from Claude Desktop
2. **Screen Capture Module**: Cross-platform screen capture using `mss`, `pygetwindow`, and platform-specific APIs
3. **Vision LLM Integration**: Unified interface for multiple Vision LLM providers
4. **Context Engine**: Background monitoring system for window changes and system events
5. **Tool Framework**: Extensible tool system for adding new visual capabilities

### Supported Platforms

- **Windows**: Uses `mss` for screen capture, `pygetwindow` for window management
- **macOS**: Uses `screencapture` command and Quartz framework
- **Linux**: Uses `xwininfo`, `import` command, and X11 libraries

### Vision LLM Providers

- **Anthropic Claude**: Claude 3.5 Sonnet with vision capabilities
- **OpenAI GPT-4V**: GPT-4 Vision Preview model
- **Google Gemini**: Gemini Pro Vision model

## ⚙️ Configuration Options

All configuration can be set via environment variables or `.env` file:

### Server Settings
- `VISION_MCP_SERVER_HOST`: Server host (default: localhost)
- `VISION_MCP_SERVER_PORT`: Server port (default: 8000)
- `VISION_MCP_DEBUG`: Debug mode (default: false)

### Screen Capture
- `VISION_MCP_CAPTURE_QUALITY`: Image quality 1-100 (default: 95)
- `VISION_MCP_CAPTURE_FORMAT`: Image format (default: PNG)
- `VISION_MCP_MAX_IMAGE_SIZE`: Maximum image dimensions (default: 1920x1080)

### Video Recording
- `VISION_MCP_VIDEO_FPS`: Video frame rate (default: 30)
- `VISION_MCP_VIDEO_QUALITY`: Video quality CRF value (default: 23)
- `VISION_MCP_MAX_RECORDING_DURATION`: Max recording time in seconds (default: 300)

### Context Engine
- `VISION_MCP_CONTEXT_CHECK_INTERVAL`: Check interval in seconds (default: 1.0)
- `VISION_MCP_MAX_CONTEXT_HISTORY`: Max context history items (default: 100)

## 🧪 Development

### Running Tests

```bash
# Run all tests
python test_server.py

# Test specific components
python -c "from vision_context_mcp.core.screen_capture import ScreenCapture; import asyncio; asyncio.run(ScreenCapture().capture_fullscreen())"
```

### Adding New Tools

1. Create a new tool class inheriting from `BaseTool`
2. Implement the `execute` method
3. Add tool registration in `server.py`
4. Update tool imports in `tools/__init__.py`

### Debugging

Enable debug mode for detailed logging:
```bash
export VISION_MCP_DEBUG=true
export VISION_MCP_LOG_LEVEL=DEBUG
python -m vision_context_mcp
```

## 🔒 Security & Privacy

- **Local Processing**: All screen capture and analysis happens locally
- **API Communication**: Only base64-encoded images are sent to Vision LLM APIs
- **No Data Storage**: No persistent storage of captured images or videos
- **Sandboxed Execution**: MCP server runs in isolated environment
- **Permission-based**: Requires explicit user permission through Claude Desktop

## 🐛 Troubleshooting

### Common Issues

1. **Screen capture fails on Linux**: Install required X11 dependencies
   ```bash
   sudo apt-get install python3-xlib imagemagick
   ```

2. **Active window capture not working**: Some systems may not support active window detection, fallback to fullscreen

3. **Vision LLM API errors**: Check API keys and rate limits

4. **Claude Desktop not detecting server**: Verify configuration file path and syntax

### Logs

Check logs for debugging:
- Server logs: Console output when running the server
- Test logs: Output from `test_server.py`
- Claude Desktop logs: Check Claude Desktop's MCP server logs

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details

## 🙏 Acknowledgments

- [Model Context Protocol](https://github.com/modelcontextprotocol) for the MCP specification
- [Anthropic](https://anthropic.com) for Claude and MCP support
- [OpenAI](https://openai.com) for GPT-4V API
- [Google](https://ai.google.dev) for Gemini Pro Vision
- Open source libraries: `mss`, `opencv-python`, `pygetwindow`, `Pillow`
