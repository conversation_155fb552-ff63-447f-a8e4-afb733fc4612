"""
Base tool class for Vision Context MCP Server
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Union

import structlog
from mcp.types import TextContent, ImageContent

logger = structlog.get_logger(__name__)


class BaseTool(ABC):
    """Base class for MCP tools"""
    
    def __init__(self, name: str, description: str):
        self.name = name
        self.description = description
        self.logger = logger.bind(tool=name)
    
    @abstractmethod
    async def execute(self, arguments: Dict[str, Any]) -> List[Union[TextContent, ImageContent]]:
        """
        Execute the tool with given arguments
        
        Args:
            arguments: Tool arguments
            
        Returns:
            List of content items (text or image)
        """
        pass
    
    def _create_text_content(self, text: str) -> TextContent:
        """Create text content"""
        return TextContent(type="text", text=text)
    
    def _create_image_content(
        self, 
        data: str, 
        mime_type: str = "image/png"
    ) -> ImageContent:
        """Create image content"""
        return ImageContent(
            type="image",
            data=data,
            mimeType=mime_type
        )
    
    def _validate_arguments(
        self, 
        arguments: Dict[str, Any], 
        required_fields: List[str]
    ) -> None:
        """Validate required arguments"""
        missing_fields = [
            field for field in required_fields 
            if field not in arguments
        ]
        
        if missing_fields:
            raise ValueError(
                f"Missing required arguments: {', '.join(missing_fields)}"
            )
    
    def _log_execution_start(self, arguments: Dict[str, Any]) -> None:
        """Log tool execution start"""
        self.logger.info("Tool execution started", arguments=arguments)
    
    def _log_execution_end(self, success: bool = True, error: str = None) -> None:
        """Log tool execution end"""
        if success:
            self.logger.info("Tool execution completed successfully")
        else:
            self.logger.error("Tool execution failed", error=error)
