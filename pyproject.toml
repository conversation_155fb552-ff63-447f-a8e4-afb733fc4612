[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "vision-context-mcp"
version = "0.1.0"
description = "MCP Server for real-time visual perception capabilities for AI models"
authors = [
    {name = "Vision Context Team", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.9"
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]
dependencies = [
    "mcp>=1.0.0",
    "jsonrpcserver>=5.0.0",
    "pydantic>=2.0.0",
    "mss>=9.0.0",
    "pygetwindow>=0.0.9",
    "pyautogui>=0.9.54",
    "Pillow>=10.0.0",
    "opencv-python>=4.8.0",
    "av>=10.0.0",
    "anthropic>=0.25.0",
    "openai>=1.0.0",
    "google-generativeai>=0.3.0",
    "aiohttp>=3.9.0",
    "python-dotenv>=1.0.0",
    "structlog>=23.0.0",
    "rich>=13.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
]

[project.urls]
Homepage = "https://github.com/vision-context/mcp-server"
Repository = "https://github.com/vision-context/mcp-server"
Documentation = "https://vision-context.github.io/mcp-server"
"Bug Tracker" = "https://github.com/vision-context/mcp-server/issues"

[project.scripts]
vision-context-mcp = "vision_context_mcp.server:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["vision_context_mcp*"]

[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short"
asyncio_mode = "auto"
