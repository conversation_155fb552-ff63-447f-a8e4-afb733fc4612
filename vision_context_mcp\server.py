"""
Vision Context MCP Server

Ana MCP server implementasyonu
"""

import asyncio
import sys
from typing import Any, Dict, List, Optional

import structlog
from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import (
    CallToolRequest,
    CallToolResult,
    ListToolsRequest,
    ListToolsResult,
    Tool,
    TextContent,
    ImageContent,
)

from .config.settings import get_settings
from .tools import ScreenAnalyzerTool, VideoRecorderTool, VisionQueryTool
from .utils.helpers import setup_logging

logger = structlog.get_logger(__name__)


class VisionContextMCPServer:
    """Vision Context MCP Server"""
    
    def __init__(self):
        self.settings = get_settings()
        self.server = Server("vision-context-mcp")
        self.tools = {}
        self._setup_tools()
        self._setup_handlers()
    
    def _setup_tools(self) -> None:
        """Setup MCP tools"""
        self.tools = {
            "analyze_screen_context": ScreenAnalyzerTool(),
            "record_screen": VideoRecorderTool(),
            "query_vision_about_current_view": VisionQueryTool(),
        }
        logger.info("Tools initialized", tools=list(self.tools.keys()))
    
    def _setup_handlers(self) -> None:
        """Setup MCP request handlers"""
        
        @self.server.list_tools()
        async def handle_list_tools() -> ListToolsResult:
            """List available tools"""
            tools = []
            
            # analyze_screen_context tool
            tools.append(Tool(
                name="analyze_screen_context",
                description="Aktif pencere veya tam ekranı analiz eder",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "mode": {
                            "type": "string",
                            "enum": ["fullscreen", "active_window"],
                            "description": "Hangi ekran bölgesi analiz edilecek"
                        },
                        "analysis_prompt": {
                            "type": "string",
                            "description": "Özel analiz sorusu (opsiyonel)",
                            "default": "Bu görüntüde ne görüyorsun? Detaylı analiz yap."
                        }
                    },
                    "required": ["mode"]
                }
            ))
            
            # record_screen tool
            tools.append(Tool(
                name="record_screen",
                description="Ekranı belirtilen süre boyunca kaydeder",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "duration": {
                            "type": "integer",
                            "description": "Kayıt süresi (saniye)",
                            "minimum": 1,
                            "maximum": 300
                        },
                        "mode": {
                            "type": "string",
                            "enum": ["fullscreen", "active_window"],
                            "description": "Kayıt modu",
                            "default": "fullscreen"
                        }
                    },
                    "required": ["duration"]
                }
            ))
            
            # query_vision_about_current_view tool
            tools.append(Tool(
                name="query_vision_about_current_view",
                description="Görsel analiz sonucu hakkında Vision LLM'e soru sorar",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "question": {
                            "type": "string",
                            "description": "Sorulmak istenen analiz sorusu"
                        },
                        "context": {
                            "type": "string",
                            "description": "Ek bağlam bilgisi (opsiyonel)"
                        }
                    },
                    "required": ["question"]
                }
            ))
            
            logger.info("Listed tools", count=len(tools))
            return ListToolsResult(tools=tools)
        
        @self.server.call_tool()
        async def handle_call_tool(
            name: str, 
            arguments: Optional[Dict[str, Any]]
        ) -> CallToolResult:
            """Handle tool calls"""
            logger.info("Tool called", name=name, arguments=arguments)
            
            if name not in self.tools:
                logger.error("Unknown tool", name=name)
                return CallToolResult(
                    content=[TextContent(
                        type="text",
                        text=f"Bilinmeyen tool: {name}"
                    )],
                    isError=True
                )
            
            try:
                tool = self.tools[name]
                result = await tool.execute(arguments or {})
                
                logger.info("Tool executed successfully", name=name)
                return CallToolResult(content=result)
                
            except Exception as e:
                logger.error("Tool execution failed", name=name, error=str(e))
                return CallToolResult(
                    content=[TextContent(
                        type="text",
                        text=f"Tool çalıştırma hatası: {str(e)}"
                    )],
                    isError=True
                )
    
    async def run(self) -> None:
        """Run the MCP server"""
        logger.info("Starting Vision Context MCP Server")
        
        # Initialize server options
        options = InitializationOptions(
            server_name="vision-context-mcp",
            server_version="0.1.0",
            capabilities={
                "tools": {},
                "logging": {},
            }
        )
        
        async with stdio_server() as (read_stream, write_stream):
            await self.server.run(
                read_stream,
                write_stream,
                options
            )


async def main() -> None:
    """Main entry point"""
    settings = get_settings()
    
    # Setup logging
    setup_logging(
        level=settings.log_level,
        log_file=settings.log_file,
        use_rich=True
    )
    
    logger.info("Vision Context MCP Server starting", version="0.1.0")
    
    try:
        server = VisionContextMCPServer()
        await server.run()
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error("Server error", error=str(e))
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
