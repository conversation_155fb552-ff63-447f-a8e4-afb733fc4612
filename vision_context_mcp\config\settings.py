"""
Configuration settings for Vision Context MCP Server
"""

import os
from typing import Optional
from pydantic import BaseSettings, Field


class Settings(BaseSettings):
    """Application settings"""
    
    # Server Configuration
    server_host: str = Field(default="localhost", description="MCP server host")
    server_port: int = Field(default=8000, description="MCP server port")
    debug: bool = Field(default=False, description="Debug mode")
    
    # Vision LLM API Keys
    anthropic_api_key: Optional[str] = Field(default=None, description="Anthropic API key for Claude")
    openai_api_key: Optional[str] = Field(default=None, description="OpenAI API key for GPT-4V")
    google_api_key: Optional[str] = Field(default=None, description="Google API key for Gemini")
    
    # Screen Capture Settings
    capture_quality: int = Field(default=95, description="Image capture quality (1-100)")
    max_image_size: tuple[int, int] = Field(default=(1920, 1080), description="Maximum image dimensions")
    capture_format: str = Field(default="PNG", description="Image capture format")
    
    # Video Recording Settings
    video_fps: int = Field(default=30, description="Video recording FPS")
    video_quality: int = Field(default=23, description="Video quality (CRF value)")
    max_recording_duration: int = Field(default=300, description="Maximum recording duration in seconds")
    
    # Context Engine Settings
    context_check_interval: float = Field(default=1.0, description="Context check interval in seconds")
    max_context_history: int = Field(default=100, description="Maximum context history items")
    
    # Logging
    log_level: str = Field(default="INFO", description="Logging level")
    log_file: Optional[str] = Field(default=None, description="Log file path")
    
    # Security
    allowed_domains: list[str] = Field(default=["localhost", "127.0.0.1"], description="Allowed domains")
    require_auth: bool = Field(default=False, description="Require authentication")
    
    class Config:
        env_file = ".env"
        env_prefix = "VISION_MCP_"
        case_sensitive = False


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get application settings"""
    return settings


def update_settings(**kwargs) -> None:
    """Update settings with new values"""
    global settings
    for key, value in kwargs.items():
        if hasattr(settings, key):
            setattr(settings, key, value)
